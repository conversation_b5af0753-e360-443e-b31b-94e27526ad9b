require("工具函数")
---------------------------------------------------------------------------------------------------------
-- 请根据压缩包内的 对接指导PDF 进行配置
---------------------------------------------------------------------------------------------------------

app = {
	mode = 0 , -- 该值为 0 时为计时模式，该值为 1 时为计次模式
	appId = "6837cd3e" , -- 开发后台对应项目的项目ID
	secretKey = "DKMlCjlDg6EO7v8p" , --开发后台对应项目的项目密钥
	version = 2.41, -- 当开发后台设置的版本号大于该版本号时，执行热更新
	changeMode = 0 , --[[该值为 0 时为顶号换机 ， 该值为 1 时为卡密换机
	【顶号换机】当卡号在第二台设备使用时，不用换机码就可以换机，同时前一台设备自动解绑下线
	【卡密换机】当卡号在第二台设备使用时，需要输入换机码才能换机，用户购买换机码 ，二次为你带来收益，强烈推荐]]
	success = false ,
	
	-- hotLink 定义热更新链接
	hotLink = 'https://user.ks.018888.xyz/update/6837cd3e?key=156db9ccf85438a73812d1a9e2df25c3' , -- 此为新版，此项请留空 并 把热更新链接复制粘贴到：我的项目-> 对应项目的 -> 下载网址选项即可
}

---------------------------------------------------------------------------------------------------------
-- 这部分为热更新相关代码
---------------------------------------------------------------------------------------------------------
function upBtnClick(btn)
	if btn == 1 then
		if upMode == true then
			exitScript()
		end
	else
		function progress(pos)
			toast("更新下载进度【"..pos.."】" , 0 , 0 , 18)
		end
		toast('正在执行更新，请耐心等待' , 0 , 0 , 18)
		local path = getWorkPath()..'/script.lrj'
		if downloadFile(download_url , path , progress) == 0 then
			sleep(200)
			if fileMD5(path) ~= app.md5 then
				showMessage('更新包下载不完整，请重试')
				restartScript()
			end
			
			installLrPkg(path)
			toast('更新成功，重启脚本' , 0 , 0 , 18)
		else
			showMessage('更新包下载失败，请重试')
		end
		
		sleep(2000)
		restartScript()
	end
	
	flag = false
end

function onClose()
	flag = false
end
function showMessage(msg)
	local w , h , width
	w , h = getDisplaySize()
	if w >= 1000 then
		width = 750
	else
		width = 550
	end
	
	ui.dismiss('检测更新')
	
	--标题
	ui.newLayout("提示信息" , width , - 2)
	ui.setTextColor("提示信息" , "#FFFFFFFF")
	ui.setTitleBackground("提示信息" , "#FF800000")
	
	--文本
	ui.addLine("提示信息" , "lineId1" , - 1 , 2)
	ui.newRow("提示信息" , "row1")
	ui.addTextView("提示信息" , "label1" , msg)
	ui.setPadding("label1" , 30 , 30 , 30 , 10)
	
	ui.setOnClose("提示信息" , "onClose()")
	ui.show("提示信息")
	flag = true
	while flag do
		sleep(200)
	end
end

function 显示更新UI(ver , msg , force)
	local w , h , width
	w , h = getDisplaySize()
	if w >= 1000 then
		width = 950
	else
		width = 700
	end
	
	-- 标题
	local title = '检测更新'
	ui.newLayout(title , width , - 2)
	ui.setTextColor(title , "#FFFFFFFF")
	ui.setTitleBackground(title , "#FF800000")
	-- 文本
	ui.addLine(title , "lineId1" , - 1 , 2)
	ui.newRow(title , "row1")
	ui.addTextView(title , "label3" , "检测到新版本 V " .. ver)
	ui.newRow(title , "row1")
	ui.addTextView(title , "label4" , msg:gsub("<br>" , "\n"))
	ui.setPadding("label3" , 20 , 20 , 20 , 0)
	ui.setPadding("label4" , 20 , 20 , 10 , 10)
	
	-- 按钮
	ui.newRow(title , "row1")
	ui.addLine(title , "lineId2" , - 1 , 1)
	ui.newRow(title , "row1")
	
	if force == true then
		ui.setOnClose(title , "upBtnClick(2)")
		ui.show(title)
	else
		ui.addTextView(title , "label5" , "        ")
		ui.addButton(title , "checkUpBtn1" , '忽 略')
		ui.addTextView(title , "label6" , "      ")
		
		ui.addButton(title , "checkUpBtn2" , "继 续")
		ui.setBackground("checkUpBtn1" , "#FF800000")
		ui.setBackground("checkUpBtn2" , "#FF004000")
		
		ui.setOnClick("checkUpBtn1" , "upBtnClick(1)")
		ui.setOnClick("checkUpBtn2" , "upBtnClick(2)")
		ui.setPadding("checkUpBtn1" , 15 , 15 , 15 , 15)
		ui.setPadding("checkUpBtn2" , 15 , 15 , 15 , 15)
		ui.show(title , false)
	end
	
	flag = true
	while flag do
		sleep(200)
	end
	
	ui.dismiss(title)
end

function 在线热更新()
	if app.hotLink ~= '' then
		local res , status = httpGet(app.hotLink .. '&version=' .. app.version , 10)
		if status == 200 then
			local ok , res = pcall(jsonLib.decode , res)
			if ok then
				if res.version > app.version then
					upMode = res.force
					download_url = res.download_url
					if download_url == '' then
						toast('开发者没有上传更新包' , 0 , 0 , 18)
					else
						app.md5 = res.md5
						显示更新UI(res.version , res.msg , res.force)
					end
				end
			end
		end
	end
end

---------------------------------------------------------------------------------------------------------
-- 以下为网络验证相关函数
---------------------------------------------------------------------------------------------------------
-- 发起POST请求
import('java.lang.*')
import('java.util.*')
import('com.nx.assist.lua.LuaEngine')
function postRequest(apiRoute , args)
	local plaintextData = table.concat(args , "|")
	local encryptedData = aesEncryp(plaintextData )
	table.insert(args , 1 , apiRoute)
	table.insert(args , app.secretKey)
	sign = MD5(table.concat(args))
	
	local params = {
		data = encryptedData ,
		sign = sign ,
	}
	
	local headers = { ["User-Agent"] = 'lua_'..app.appId }
	local result = LuaEngine.httpPost(app.api..apiRoute , params , headers , 60)
	
	if checkIsDebug() then
		table.insert(args , sign)
		print("当前接口：" , app.api)
		print("请求参数：" , args)
		print("加密结果：" , encryptedData)
		print("返回数据：" , result)
	end
	
	local ok , result = pcall(jsonLib.decode , result)
	if not ok then
		result = {code = 403 , msg = aesEncryp('网络请求异常，请更换线路尝试')}
	end
	
	if not tonumber(result.code) then
		result.code = tonumber(aesDecrypt(result.code))
	end
	
	return result
end

-- 返回项目配置信息
function 项目信息()
	local t = os.time()
	local apiRoute = "Project/appInfo/"..app.appId
	local args = {0 , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		result.appName = aesDecrypt(result.appName)
		result.version = aesDecrypt(result.version)
		result.appStatus = tonumber( aesDecrypt(result.appStatus))
		result.noticeStatus = tonumber( aesDecrypt(result.noticeStatus))
		result.trialCount = aesDecrypt(result.trialCount)
		result.trialTime = aesDecrypt(result.trialTime)
		result.changeTime = aesDecrypt(result.changeTime)
		result.webSite = aesDecrypt(result.webSite)
		result.shopLink = aesDecrypt(result.shopLink)
		result.downloadLink = aesDecrypt(result.downloadLink)
		result.notice = aesDecrypt(result.notice)
		result.contactInfo = aesDecrypt(result.contactInfo)
	else
		result.msg = aesDecrypt(result.msg)
	end
	
	return result
end

-- 调用远程函数
-- 成功返回函数结果，失败返回空字符串
function 远程函数(函数名称 , 函数参数)
	local t = os.time()
	local funName = strTrim(函数名称)
	local params = strTrim(函数参数)
	local apiRoute = "Variable/runFunction/"..app.appId
	local args = {funName , params , app.token , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		result.funRes = aesDecrypt(result.result)
		result.timeStamp = aesDecrypt(result.timeStamp)
		local sign = MD5(result.code..result.funRes..result.timeStamp..app.secretKey)
		if result.sign ~= sign then
			setTimer(restartScript , 1000)
		end
		if math.abs(result.timeStamp - t) > 60 then
			setTimer(restartScript , 1000)
		end
		
		return result.funRes
	else
		if result.code == 504 then
			setTimer(restartScript , 1000)
		end
		
		return ''
	end
end

-- 添加远程变量
-- 成功返回 true，失败返回 false
function 添加变量(变量名称 , 变量值 , 功能备注)
	local t = os.time()
	local varName = strTrim(变量名称)
	local value = strTrim(变量值)
	local remark = strTrim(功能备注)
	local apiRoute = "Variable/addVal/"..app.appId
	local args = {varName , value , remark , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		return true
	else
		return false
	end
end

-- 读取云端变量
-- 成功返回变量值，失败返回空字符串
function 读取变量(变量名称)
	local t = os.time()
	local varName = strTrim(变量名称)
	local apiRoute = "Variable/getVal/"..app.appId
	local args = {varName , app.token , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		result.value = aesDecrypt(result.value)
		result.timeStamp = aesDecrypt(result.timeStamp)
		local sign = MD5(result.code..result.value..result.timeStamp..app.secretKey)
		if result.sign ~= sign then
			setTimer(restartScript , 1000)
		end
		if math.abs(result.timeStamp - t) > 60 then
			setTimer(restartScript , 1000)
		end
		
		return result.value
	else
		if result.code == 504 then
			setTimer(restartScript , 1000)
		end
		
		return ''
	end
end

-- 更新远程变量
-- 成功返回 true，失败返回 false
function 更新变量(变量名称 , 新变量值)
	local t = os.time()
	local value = strTrim(新变量值)
	local varName = strTrim(变量名称)
	local apiRoute = "Variable/setVal/"..app.appId
	local args = {varName , value , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		return true
	else
		return false
	end
end

-- 更新卡号备注
-- 成功返回 true，失败返回 false
function 更新备注(卡号或账号 , 备注信息)
	local t = os.time()
	local authCode = strTrim(卡号或账号)
	local remark = strTrim(备注信息)
	local apiRoute = "Variable/setRemark/"..app.appId
	local args = {authCode , remark , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		return true
	else
		return false
	end
end

-- 授权相关信息查询接口
-- 账号密码模式密码参数传入空字符，详见开发文档
function 授权查询(卡号或账号 , 密码)
	local t = os.time()
	local password = strTrim(密码)
	local authCode = strTrim(卡号或账号)
	local apiRoute = "License/getKeyInfo/"..app.appId
	local args = {authCode , password , t}
	return postRequest(apiRoute , args)
end

-- 网络验证综合接口
-- 根据提交不同的请求参数，实现不同的认证模式，请参照开发文档使用
function 网络验证(卡号或账号 , 密码)
	local sign1
	local t = os.time()
	local password = strTrim(密码)
	local authCode = strTrim(卡号或账号)
	local apiRoute = "License/verify/" .. app.appId
	local args = {app.mode , authCode , password , getMachineCode() , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		result.token = aesDecrypt(result.token)
		result.remark = aesDecrypt(result.remark)
		result.timeStamp = aesDecrypt(result.timeStamp)
		if app.mode == 0 then
			result.endDate = aesDecrypt(result.endDate)
			result.msg = '认证成功\n到期时间：' ..result.endDate
			sign = MD5(result.code..result.endDate..result.token..result.remark..result.timeStamp..app.secretKey)
		else
			result.surplusCount = aesDecrypt(result.surplusCount)
			result.msg = '认证成功\n您还剩余 ' ..result.surplusCount .. ' 次可用次数'
			sign = MD5(result.code..result.surplusCount..result.token..result.remark..result.timeStamp..app.secretKey)
		end
		
		if result.sign ~= sign then
			result.msg = '签 名 校 验 失 败'
			result.code = 503
		end
		if math.abs(result.timeStamp - t) > 60 then
			result.msg = '签 名 已 过 期'
			result.code = 502
		end
		
		app.token = result.token
		app.remark = result.remark
		beginThread(心跳守护 , result.token) -- 自动调用心跳守护
	elseif result.code == 104 then
		自助换机(authCode , password) -- 自动调用自助换机
		return result
	else
		result.msg = aesDecrypt(result.msg)
	end
	
	showMessage(result.msg)
	return result
end

-- 心跳守护，自动调用
function 心跳守护(token)
	sleep(1000 * 15)
	local result
	local count = 0
	local cycles = 3
	if not checkIsDebug() then
		-- 循环次数不得低于20, 建议 50 - 100 为最佳！过于频繁访问服务器，你的IP将被暂时或永久封禁
		-- 心跳验证频率过快，如果网络质量不好，还会导致脚本运行时容易中断，因为网络访问总有不确定因素
		cycles = 60 -- 心跳循环次数 - 如果想调整心跳间隔，修改该值即可，不得低于20, 建议 50 - 100 为最佳
	end
	local apiRoute = "License/heartBeat/"..app.appId
	repeat
		local t = os.time()
		local args = {token , getMachineCode() , t}
		result = postRequest(apiRoute , args)
		local code = result.code
		if checkIsDebug() then
			print("心跳状态："..code.."，当前心跳间隔 "..cycles * 10 .." 秒 "..os.date("%H:%M:%S" , t))
		end
		if code == 200 then
			count = 0
			result.timeStamp = aesDecrypt(result.timeStamp)
			if app.mode == 0 then
				result.endDate = aesDecrypt(result.endDate)
				sign = MD5(code..result.endDate..result.timeStamp..app.secretKey)
			else
				result.surplusCount = aesDecrypt(result.surplusCount)
				sign = MD5(code..result.surplusCount..result.timeStamp..app.secretKey)
			end
			
			if result.sign ~= sign then
				result.msg = '签 名 校 验 失 败'
				break
			end
			if math.abs(result.timeStamp - t) > 60 then
				result.msg = '签 名 已 过 期'
				break
			end
			
			for i = 1 , cycles do
				sleep(1000 * 10)
				if app.mode == 0 then
					if os.time() - strTime(result.endDate) > 0 then
						result.msg = '授 权 已 过 期'
						count = 100
						break
					end
				end
			end
		else
			result.msg = aesDecrypt(result.msg)
			if code == 100 or code == 101 or code == 102 or code == 103 or code == 201 or code == 203 or code == 501 or code == 502 or code == 503 or code == 504 or code == 600 then
				break
			else
				sleep(1000 * 10)
				count = count + 1
			end
		end
	until count >= 10
	
	if checkIsDebug() then
		print('心跳失败：'..result.msg)
	end
	setTimer(restartScript , 1000) -- 重启脚本
end

-- 用户换机综合接口
-- 卡号模式传入卡号和空，账号模式传入账号和密码
function 自助换机(账号 , 密码)
	function chgClose()
		app.chgCard = strTrim(ui.getValue("editId1"))
		flag = false
	end
	
	local msg
	if app.changeMode == 0 then
		msg = "检测到您已更换设备\n确定要绑定到当前设备使用吗？"
	else
		msg = "检测到您已更换设备\n如需绑定当前设备使用请输入换机码"
	end
	
	local w , h , width
	w , h = getDisplaySize()
	if w >= 1000 then
		width = 800
	else
		width = 700
	end
	
	--标题
	ui.newLayout("自助换机" , width , - 2)
	ui.setTextColor("自助换机" , "#FFFFFFFF")
	ui.setTitleBackground("自助换机" , "#FF800000")
	
	--文本
	ui.addLine("自助换机" , "lineId1" , - 1 , 2)
	ui.newRow("自助换机" , "row1")
	ui.addTextView("自助换机" , "label1" , msg)
	ui.newRow("自助换机" , "row1")
	ui.newRow("自助换机" , "row1")
	ui.setPadding("label1" , 30 , 30 , 30 , 20)
	
	if app.changeMode == 0 then
		app.chgCard = ""
	else
		ui.addTextView("自助换机" , "label2" , "换机码：")
		ui.addEditText("自助换机" , "editId1" , "")
		ui.setPadding("label2" , 30 , 20 , 30 , 20)
	end
	
	ui.setOnClose("自助换机" , "chgClose()")
	ui.show("自助换机")
	flag = true
	while flag do
		sleep(200)
	end
	
	local t = os.time()
	local authCode = strTrim(账号)
	local password = strTrim(密码)
	local apiRoute = "Device/change/" .. app.appId
	local args = {app.changeMode , authCode , password , app.chgCard , getMachineCode() , t}
	local result = postRequest(apiRoute , args)
	result.msg = aesDecrypt(result.msg)
	toast(result.msg , 0 , 0 , 18)
	
	return result
end

-- 注册与试用综合接口
-- 账号密码为空视为获取试用卡号，反之视为注册账号
function 试用注册(账号 , 密码)
	local data
	local t = os.time()
	local username = strTrim(账号)
	local password = strTrim(密码)
	local apiRoute = "Trial/getCard/" .. app.appId
	local args = {app.mode , username , password , getMachineCode() , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		local lineId1 , lineId2
		result.trialCount = aesDecrypt(result.trialCount)
		result.usedCount = aesDecrypt(result.usedCount)
		if username == "" then
			result.authCode = aesDecrypt(result.authCode)
			if app.mode == 0 then
				result.endDate = aesDecrypt(result.endDate)
				lineId1 = "卡号：" .. result.authCode .. "\n到期时间：" .. result.endDate
			else
				result.surplusCount = aesDecrypt(result.surplusCount)
				lineId1 = "卡号：" .. result.authCode .. "\n系统免费赠送 " .. result.surplusCount .. " 次使用机会"
			end
			
			lineId2 = "您已获取 " .. result.usedCount .. " 次试用，还能获取 " .. result.trialCount - result.usedCount .. " 次"
		else
			if app.mode == 0 then
				result.endDate = aesDecrypt(result.endDate)
				toast('注册成功，可免费使用到：\n'..result.endDate , 0 , 0 , 18)
			else
				result.surplusCount = aesDecrypt(result.surplusCount)
				toast('注册成功，获得 '..result.surplusCount..' 次免费使用次数' , 0 , 0 , 18)
			end
		end
		
		local w , h , width
		w , h = getDisplaySize()
		if w >= 1000 then
			width = 780
		else
			width = 600
		end
		
		--标题
		ui.newLayout("提示信息" , width , - 2)
		ui.setTextColor("提示信息" , "#FFFFFFFF")
		ui.setTitleBackground("提示信息" , "#FF800000")
		
		--文本
		ui.addLine("提示信息" , "lineId1" , - 1 , 2)
		ui.newRow("提示信息" , "row1")
		ui.addTextView("提示信息" , "label1" , lineId1)
		ui.newRow("提示信息" , "row1")
		
		ui.addLine("提示信息" , "lineId2" , - 1 , 1)
		ui.newRow("提示信息" , "row1")
		ui.addTextView("提示信息" , "label2" , lineId2)
		
		ui.setPadding("label1" , 30 , 20 , 20 , 20)
		ui.setPadding("label2" , 30 , 20 , 20 , 10)
		
		ui.setOnClose("提示信息" , "onClose()")
		ui.show("提示信息")
		flag = true
		while flag do
			sleep(200)
		end
	else
		result.msg = aesDecrypt(result.msg)
		toast(result.msg , 0 , 0 , 18)
	end
	
	return result
end

-- 充值综合接口
-- 使用新卡对已激活的旧卡或账号进行充值
function 以卡充卡(卡号或账号 , 充值卡号)
	local t = os.time()
	local keyOrUsername = strTrim(卡号或账号)
	local newKey = strTrim(充值卡号)
	local apiRoute = "License/recharge/"..app.appId
	local args = {keyOrUsername , newKey , t}
	local result = postRequest(apiRoute , args)
	if result.code == 200 then
		if app.mode == 0 then
			result.duration = aesDecrypt(result.duration)
			toast('充 值 成 功\n有 效 时 间 增 加 ' ..result.duration..' 分 钟' , 0 , 0 , 18)
		else
			result.newCount = aesDecrypt(result.newCount)
			toast('充 值 成 功\n可 用 次 数 增 加 ' ..result.newCount..' 次' , 0 , 0 , 18)
		end
	else
		result.msg = aesDecrypt(result.msg)
		toast(result.msg , 0 , 0 , 18)
	end
	
	return result
end

function 线路选择(handle)
	local line = getUISelected(handle , 0 , "idRadiobox")
	if line == 0 then
		app.api = "http://api.kushao.net/v3/"
	elseif line == 1 then
		app.api = "http://api.kushao.018888.xyz/v3/"
	else
		local res , code = httpGet("https://wwzd.lanzouy.com/irQ6n2uuwy0j")
		if code == 200 then
			local ret = splitStr(res , "|")
			app.api = "http://"..ret[2]..":"..ret[3].."v3/"
		else
			app.api = "http://api.ks.186777.xyz/v3/"
		end
	end
	if checkIsDebug() then
		print("当前api：" .. app.api)
	end
	return app.api
end

-- 取设备ID
function getMachineCode()
	local machineCode = getDeviceId()
	
	if deviceId == '' then
		machineCode = getModel()..getBrand()..getHardware()..getSubscriberId()..getWifiMac()
	end
	
	return MD5(machineCode )
end

-- 打开网址
function openUrl(url)
	local url = strTrim(url)
	if string.find(url , "http" , 1) ~= 1 then
		url = "http://"..url
	end
	exec("am start -a android.intent.action.VIEW -d \""..url.."\"")
end

-- 日期转时间戳
function strTime(datestr)
	local fun = string.gmatch(datestr , "%d+")
	local year = fun()
	local month = fun()
	local day = fun()
	local hour = fun()
	local min = fun()
	local sec = fun()
	if year > "2037" then
		year = "2037"
	end
	return os.time({year = year , month = month , day = day , hour = hour , min = min , sec = sec})
end

-- AES加密
function aesEncryp(data)
	return encodeBase64(cryptLib.aes_crypt(data , app.secretKey , "encrypt" , "ecb" , true))
end

-- AES解密
function aesDecrypt(data)
	return cryptLib.aes_crypt(decodeBase64(data) , app.secretKey , "decrypt" , "ecb" , true)
end

function strTrim(str)
	return (str:gsub("^%s+" , ""):gsub("%s+$" , ""))
end

--[===[发布APK前，删除以上所有内容，然后把配置好的 authSDK.lua 的所有内容复制到本脚本开头，最后把 authSDK.lua 文件删除再打包APK
为什么要这么做? 因懒人精灵自身特性，打包的APK可以替换引入的模块文件，因此发布APK前把 authSDK.lua 和 脚本合并到一个文件]===]

-- 【性能优化】卡密读取模块 - 高性能缓存 + 智能新卡密检测
-- 核心特性：
-- 1. 高性能缓存：首次读取后缓存5分钟，后续访问毫秒级响应
-- 2. 智能检测：自动检测用户更换新卡密，确保新卡密立即生效
-- 3. 文件监控：检测配置文件外部修改，自动失效过时缓存
-- 4. 验证保护：验证失败时清除缓存，避免错误缓存干扰
local 卡密缓存模块 = {
    -- 预计算的路径缓存
    配置文件路径 = nil,
    配置目录路径 = nil,

    -- 内存缓存
    缓存的卡密 = nil,
    缓存时间戳 = 0,
    缓存有效期 = 300, -- 5分钟缓存有效期
    文件修改时间 = 0, -- 文件最后修改时间

    -- 初始化路径（只执行一次）
    初始化路径 = function(self)
        if not self.配置文件路径 then
            -- 使用正确的用户可访问路径
            self.配置目录路径 = "/storage/emulated/0/Download/三角洲行动定制/"
            self.配置文件路径 = self.配置目录路径 .. "config.ini"

            -- 确保目录存在
            makeDir(self.配置目录路径)
        end
        return self.配置文件路径
    end,

    -- 获取文件修改时间
    获取文件修改时间 = function(self)
        local 文件路径 = self:初始化路径()
        local file = io.open(文件路径, "r")
        if file then
            file:close()
            -- 使用文件大小和当前时间的组合作为简单的修改检测
            local size = 0
            local f = io.open(文件路径, "r")
            if f then
                local content = f:read("*all")
                size = content and #content or 0
                f:close()
            end
            return size
        end
        return 0
    end,

    -- 检查缓存是否有效（增强版）
    缓存是否有效 = function(self)
        if not self.缓存的卡密 then
            return false
        end

        -- 检查时间有效性
        local 时间有效 = (os.time() - self.缓存时间戳) < self.缓存有效期
        if not 时间有效 then
            return false
        end

        -- 检查文件是否被修改
        local 当前文件修改时间 = self:获取文件修改时间()
        if 当前文件修改时间 ~= self.文件修改时间 then
            -- 文件被修改，缓存失效
            return false
        end

        return true
    end,

    -- 清除缓存
    清除缓存 = function(self)
        self.缓存的卡密 = nil
        self.缓存时间戳 = 0
        self.文件修改时间 = 0
    end,

    -- 强制刷新缓存
    强制刷新 = function(self)
        self:清除缓存()
        -- 立即重新读取
        return self:从文件读取()
    end,

    -- 从文件读取（内部方法）
    从文件读取 = function(self)
        local 文件路径 = self:初始化路径()
        local file = io.open(文件路径, "r")
        if file then
            local 卡密 = file:read("*all")
            file:close()

            if 卡密 and 卡密 ~= "" then
                -- 更新缓存
                self.缓存的卡密 = 卡密
                self.缓存时间戳 = os.time()
                self.文件修改时间 = self:获取文件修改时间()
                return 卡密
            end
        end
        return nil
    end
}

-- 【增强版】保存卡密函数 - 智能缓存更新
function 保存卡密(卡密)
    if not 卡密 or 卡密 == "" then
        return false
    end

    local 文件路径 = 卡密缓存模块:初始化路径()
    local file = io.open(文件路径, "w")
    if file then
        file:write(卡密)
        file:close()

        -- 立即更新缓存
        卡密缓存模块.缓存的卡密 = 卡密
        卡密缓存模块.缓存时间戳 = os.time()
        卡密缓存模块.文件修改时间 = 卡密缓存模块:获取文件修改时间()

        return true
    end
    return false
end

-- 【增强版】读取卡密函数 - 智能缓存检测
function 读取卡密()
    -- 首先检查缓存（包含文件修改检测）
    if 卡密缓存模块:缓存是否有效() then
        return 卡密缓存模块.缓存的卡密
    end

    -- 缓存无效，从文件读取并更新缓存
    return 卡密缓存模块:从文件读取()
end

-- 【增强版】缓存管理函数
function 清除卡密缓存()
    卡密缓存模块:清除缓存()
end

function 强制刷新卡密()
    return 卡密缓存模块:强制刷新()
end

function 获取卡密缓存状态()
    return {
        有缓存 = 卡密缓存模块.缓存的卡密 ~= nil,
        缓存时间 = 卡密缓存模块.缓存时间戳,
        是否有效 = 卡密缓存模块:缓存是否有效(),
        缓存有效期 = 卡密缓存模块.缓存有效期,
        文件修改时间 = 卡密缓存模块.文件修改时间
    }
end

-- 【核心功能】检测用户是否更换了新卡密
function 检测卡密变化(新卡密)
    if not 新卡密 or 新卡密 == "" then
        return false
    end

    local 缓存卡密 = 卡密缓存模块.缓存的卡密
    if not 缓存卡密 then
        return true -- 没有缓存，认为是新卡密
    end

    -- 去除首尾空格后比较
    local 清理后的新卡密 = string.gsub(新卡密, "^%s*(.-)%s*$", "%1")
    local 清理后的缓存卡密 = string.gsub(缓存卡密, "^%s*(.-)%s*$", "%1")

    return 清理后的新卡密 ~= 清理后的缓存卡密
end

-- 【简化版】验证前的卡密检查
function 验证前检查卡密(用户输入卡密)
    if 检测卡密变化(用户输入卡密) then
        -- 用户输入了新卡密，清除旧缓存确保使用新卡密
        清除卡密缓存()
        return true -- 返回true表示检测到变化
    end
    return false -- 返回false表示没有变化
end

-- 【新增】性能优化的读取卡密函数（带统计）
function 读取卡密_带统计()
    local 开始时间 = os.clock()
    local 使用缓存 = 卡密缓存模块:缓存是否有效()
    local 结果 = 读取卡密()
    local 耗时 = (os.clock() - 开始时间) * 1000 -- 转换为毫秒

    return 结果, {
        耗时毫秒 = 耗时,
        使用缓存 = 使用缓存,
        缓存状态 = 获取卡密缓存状态()
    }
end

function onUIEvent(handle , event , arg1 , arg2 , arg3)
	if event == "onload" then
		线路选择(handle)
		local result = 项目信息() -- 得到项目配置信息
		
		if result.code == 200 then
			print("---------这部分代码目的是演示如何得到这些参数，可删除用不到的部分---------")
			print("项目名称:" .. result.appName)
			print("后台版本:" .. result.version)
			print("项目开关:" .. result.appStatus)
			print("公告开关:" .. result.noticeStatus)
			print("试用次数:" .. result.trialCount)
			print("试用时长:" .. result.trialTime)
			print("顶号扣时:" .. result.changeTime)
			print("官方网址:" .. result.webSite)
			print("专属网店:" .. result.shopLink)
			print("下载网址:" .. result.downloadLink)
			print("公告信息:" .. result.notice)
			print("客服信息:" .. result.contactInfo)
			print("----------------------------------------------------------------------------")
			
			app.shop = result.shopLink
			if app.hotLink == "" then
				app.hotLink = result.downloadLink
			end
			
			-- 远程公告
			if result.noticeStatus == 1 and result.notice ~= "" then
				setUIText(handle , arg1 , "idLabel3" , "官方公告")
				setUIText(handle , arg1 , "idLabel4" , result.notice)
			else
				setUIVisible(handle , arg1 , "idLabel3" , 4)
				setUIVisible(handle , arg1 , "idLabel4" , 4)
			end
			
			-- 尝试从配置文件读取卡密
			local 保存的卡密 = 读取卡密()
			if 保存的卡密 then
				setUIText(handle, arg1, "idEdit1", 保存的卡密)
			end
		end
	elseif event == "onclick" then
		if arg2 == "启动" then
			local 卡号 = getUIText(handle , arg1 , "idEdit1")

			-- 【新卡密处理】检测并处理用户更换的新卡密
			验证前检查卡密(卡号)

			local result = 网络验证(卡号 , "")
			if result.code == 200 then
				-- 验证成功，保存新卡密（自动更新缓存）
				保存卡密(卡号)
				app.success = true
				closeWindow(handle , true) -- 认证成功 => 关闭窗口，执行主脚本
			else
				-- 验证失败，清除缓存避免干扰下次验证
				清除卡密缓存()
				sleep(3000)
				app.success = false
			end
		elseif arg2 == "购卡" then
			if app.shop ~= "" then
				openUrl(app.shop)
				exitScript()
			end
		elseif arg2 == "试用" then
			-- 试用功能已停用
			showMessage("试用功能暂时不可用，请使用正式卡密")
			sleep(2000)
		elseif arg2 == "重载" then
			-- 重载时清除缓存，确保使用最新状态
			清除卡密缓存()
			closeWindow(handle , true)
			restartScript()
		elseif arg2 == "充值" then
			local 卡号 = getUIText(handle , 1 , "idEdit2")
			local 充值卡 = getUIText(handle , 1 , "idEdit3")
			以卡充卡(卡号 , 充值卡)
			setUIText(handle , 1 , "idEdit2" , "")
			setUIText(handle , 1 , "idEdit3" , "")
		end
	elseif event == "onselected" then
		线路选择(handle) -- 线路选择
	elseif event == "onclose" then
		print("关闭窗口" , arg1)
		closeWindow(handle , arg1)
	end
end

local ret = showUI("网络验证.ui" , - 1 , - 1 , onUIEvent)
if not app.success then
	sleep(3000)
	exitScript()
end

在线热更新() -- 认证成功后调用热更新，安全性更高

--[[------------------------------------------------------------------------------------------------------------
删除以下示例代码，将你的代码写到下面即可
--------------------------------------------------------------------------------------------------------------]]
--[===[超级防破解实现 :

1.关键性参数使用远程变量读写

2.涉及计算、拼接相关的代码封装成远程函数在云端计算

云端数据的每一次调用都需要签名和时间鉴权，因此不管是抓包还是爆破都没有任何用处]===]

print('这是卡密备注的信息：'..app.remark)

local 函数结果 = 远程函数('远程函数示例' , '10, 20')
print('\r\r远程函数执行的结果是：' .. 函数结果 .. '\r ')

-- local value = 读取变量("netVal003")  -- 读取云端变量
-- 更新变量("netVal003", "LUA懒人精灵云端变量更新示例")  -- 更新云端变量

-- 添加变量('varName', 'value', '云端变量功能备注')  -- 添加云端变量
-- 更新备注(app.authCode, '懒人精灵卡号备注更新示例')  -- 更新卡号备注信息